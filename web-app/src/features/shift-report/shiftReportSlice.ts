import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { persistReducer } from 'redux-persist';
import storage from 'redux-persist/lib/storage';
import { ShiftReportColumnDefaults, ShiftReportState, ShiftReportViewState } from './shiftReportTypes';

const initialState: ShiftReportState = {
  shiftReportViewState: {
    listView: 'mine',
    columns: ShiftReportColumnDefaults,
  },
};

export const shiftReportSlice = createSlice({
  name: 'shiftReport',
  initialState,
  reducers: {
    setShiftReportViewState: (state, action: PayloadAction<ShiftReportViewState>) => {
      state.shiftReportViewState = action.payload;
    },
  },
});

export const { setShiftReportViewState } = shiftReportSlice.actions;

export const shiftReportReducer = persistReducer(
  {
    key: 'shiftReport',
    storage,
    whitelist: ['shiftReportViewState'],
  },
  shiftReportSlice.reducer
);
