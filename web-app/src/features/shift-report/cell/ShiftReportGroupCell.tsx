import { GridRenderCellParams } from '@mui/x-data-grid';
import PeopleAltOutlinedIcon from '@mui/icons-material/PeopleAltOutlined';
import { ShiftReportRead } from '../shiftReportTypes';
import Cell from '../../../components/Cell';
import CellText from '../../../components/CellText';

export default function ShiftReportGroupCell(params: GridRenderCellParams<ShiftReportRead>) {
  const { row } = params;
  const groupName = row.group?.name;

  if (!groupName) {
    return null;
  }

  return (
    <Cell title={groupName}>
      <PeopleAltOutlinedIcon fontSize="small" sx={{ mr: 0.5 }} />
      <CellText>{groupName}</CellText>
    </Cell>
  );
}
