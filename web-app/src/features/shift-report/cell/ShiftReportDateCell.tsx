import { GridRenderCellParams } from '@mui/x-data-grid';
import DateRangeIcon from '@mui/icons-material/DateRange';
import ArrowRightAltIcon from '@mui/icons-material/ArrowRightAlt';
import { Box } from '@mui/material';
import { ShiftReportRead } from '../shiftReportTypes';
import Cell from '../../../components/Cell';

const getDatePlusTimeString = (date: Date) => {
  const datePlusDay = date.toDateString();
  const hours = date.getHours();
  const minutes = String(date.getMinutes()).padStart(2, '0');
  return `${datePlusDay} ${hours}:${minutes}`;
};

export default function ShiftReportDateCell(params: GridRenderCellParams<ShiftReportRead>) {
  const { row } = params;
  const startTime = new Date(row.startTime);
  const endTime = new Date(row.endTime);
  const startTimeString = getDatePlusTimeString(startTime);
  const endTimeString = getDatePlusTimeString(endTime);
  const fullDateRange = `${startTimeString} - ${endTimeString}`;

  return (
    <Cell title={fullDateRange}>
      <Box display="flex" alignItems="center">
        <DateRangeIcon fontSize="small" sx={{ mr: 0.5 }} />
        <Box sx={{ mr: 1 }}>{startTimeString}</Box>
        <ArrowRightAltIcon fontSize="small" sx={{ mr: 0.5 }} />
        <Box>{endTimeString}</Box>
      </Box>
    </Cell>
  );
}
