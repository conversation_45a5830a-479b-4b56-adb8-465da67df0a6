import { GridRenderCellParams } from '@mui/x-data-grid';
import LockOpenIcon from '@mui/icons-material/LockOpen';
import LockIcon from '@mui/icons-material/Lock';
import { ShiftReportRead, ShiftReportStatusDisplayMap } from '../shiftReportTypes';
import Cell from '../../../components/Cell';
import CellText from '../../../components/CellText';

export default function ShiftReportStatusCell(params: GridRenderCellParams<ShiftReportRead>) {
  const { row } = params;
  const status = row.status;
  const locked = row.locked;
  const statusDisplay = ShiftReportStatusDisplayMap[status];

  return (
    <Cell title={statusDisplay}>
      {locked ? <LockIcon fontSize="small" sx={{ mr: 0.5 }} /> : <LockOpenIcon fontSize="small" sx={{ mr: 0.5 }} />}
      <CellText>{statusDisplay}</CellText>
    </Cell>
  );
}
