import { GridRenderCellParams } from '@mui/x-data-grid';
import PersonIcon from '@mui/icons-material/Person';
import { ShiftLogRead } from '../shiftLogTypes';
import Cell from '../../../../components/Cell';
import CellText from '../../../../components/CellText';

export default function ShiftLogUserCell(params: GridRenderCellParams<ShiftLogRead>) {
  const { row } = params;
  const userName = row.createdBy?.fullName;

  if (!userName) {
    return null;
  }

  return (
    <Cell title={userName}>
      <PersonIcon fontSize="small" sx={{ mr: 0.5 }} />
      <CellText>{userName}</CellText>
    </Cell>
  );
}
