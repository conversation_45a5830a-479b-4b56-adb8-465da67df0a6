import { GridRenderCellParams } from '@mui/x-data-grid';
import { ShiftLogRead } from '../shiftLogTypes';
import Cell from '../../../../components/Cell';
import CellText from '../../../../components/CellText';

export default function SidCell(params: GridRenderCellParams<ShiftLogRead>) {
  const { row } = params;
  const sid = row.sid;

  if (!sid) {
    return null;
  }

  return (
    <Cell title={sid.toString()}>
      <CellText>{sid}</CellText>
    </Cell>
  );
}
