import { GridRenderCellParams } from '@mui/x-data-grid';
import { ShiftLogRead } from '../shiftLogTypes';
import Cell from '../../../../components/Cell';
import CellText from '../../../../components/CellText';

export default function ShiftLogTopicCell(params: GridRenderCellParams<ShiftLogRead>) {
  const { row } = params;
  const topicName = row.topic?.name;

  if (!topicName) {
    return null;
  }

  return (
    <Cell title={topicName}>
      <CellText>{topicName}</CellText>
    </Cell>
  );
}
