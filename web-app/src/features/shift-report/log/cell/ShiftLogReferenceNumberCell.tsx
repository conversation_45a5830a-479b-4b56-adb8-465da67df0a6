import { GridRenderCellParams } from '@mui/x-data-grid';
import { ShiftLogRead } from '../shiftLogTypes';
import Cell from '../../../../components/Cell';
import CellText from '../../../../components/CellText';

export default function ShiftLogReferenceNumberCell(params: GridRenderCellParams<ShiftLogRead>) {
  const { row } = params;
  const referenceNumber = row.referenceNumber;

  if (!referenceNumber) {
    return null;
  }

  return (
    <Cell title={referenceNumber}>
      <CellText>{referenceNumber}</CellText>
    </Cell>
  );
}
