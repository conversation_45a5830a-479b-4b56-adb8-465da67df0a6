import { GridRenderCellParams } from '@mui/x-data-grid';
import { ShiftLogRead } from '../shiftLogTypes';
import Cell from '../../../../components/Cell';
import CellText from '../../../../components/CellText';

export default function ShiftLogDescriptionCell(params: GridRenderCellParams<ShiftLogRead>) {
  const { row } = params;
  const description = row.description;

  if (!description) {
    return null;
  }

  return (
    <Cell title={description} sx={{ whiteSpace: 'normal', wordBreak: 'break-word' }}>
      <CellText>{description}</CellText>
    </Cell>
  );
}
