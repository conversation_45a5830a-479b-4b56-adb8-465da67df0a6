import { Link, useParams } from 'react-router-dom';
import AddIcon from '@mui/icons-material/Add';
import { useEffect, useMemo, useState } from 'react';
import { TabContext, TabPanel } from '@mui/lab';
import { Box, Paper, Tab, Tabs } from '@mui/material';
import { DataGrid, GridColDef, GridPaginationModel, GridRenderCellParams, GridSortModel } from '@mui/x-data-grid';
import { useGetCurrentUserQuery } from '../user/userApi';
import { useAppDispatch, useAppSelector } from '../../store';
import {
  ShiftReportColumn,
  ShiftReportColumnDefaults,
  ShiftReportColumnDisplayMap,
  ShiftReportColumnFieldMap,
  ShiftReportFieldSortMap,
  ShiftReportParams,
  ShiftReportRead,
  ShiftReportSort,
  ShiftReportSortField,
  ShiftReportStatus,
  ShiftReportStatusDisplayMap,
} from './shiftReportTypes';
import { PermissionType } from '../guard/guardTypes';
import { UserRole } from '../user/userTypes';
import { GuardResult } from '../guard/guardHooks';
import { useGetShiftReportsQuery } from './shiftReportApi';
import { setShiftReportViewState } from './shiftReportSlice';
import ErrorGate from '../../components/ErrorGate';
import PageTitle from '../title/Title';
import Guard from '../guard/Guard';
import ResponsiveButton from '../../components/ResponsiveButton';
import ShiftReportChipFilter from './ShiftReportChipFilter';
import ShiftReportFilterBar from './ShiftReportFilterBar';
import { DataGridCellLinkWrapper } from '../../components/DataGridCellLink';
import ShiftReportSidCell from './cell/ShiftReportSidCell';
import ShiftReportGroupCell from './cell/ShiftReportGroupCell';
import ShiftReportUserCell from './cell/ShiftReportUserCell';
import ShiftReportStatusCell from './cell/ShiftReportStatusCell';
import ShiftReportDateCell from './cell/ShiftReportDateCell';
import usePaging from '../../components/hooks/usePaging';
import NoRowsOverlay from '../../components/NoRowsOverlay';

const getShiftReportUrl = (shiftReportId: number) => `${shiftReportId}`;

function getGridModelFromSort(sort: ShiftReportSort[]): GridSortModel {
  return sort.map((s) => {
    // Find the column that maps to this sort field
    const columnEntry = Object.entries(ShiftReportColumnFieldMap).find(
      ([, fieldName]) => ShiftReportFieldSortMap[fieldName] === s.field
    );

    const field = columnEntry ? columnEntry[0] : s.field;
    return {
      field,
      sort: s.direction,
    };
  });
}

function getSortFromGridModel(gridModel: GridSortModel): ShiftReportSort[] {
  return gridModel.map((s) => {
    // Map column field to ShiftReportRead field, then to sort field
    const columnField = s.field as ShiftReportColumn;
    const readField = ShiftReportColumnFieldMap[columnField];
    const sortField = readField ? ShiftReportFieldSortMap[readField] : undefined;

    return {
      field: sortField || (s.field as ShiftReportSortField),
      direction: s.sort as 'asc' | 'desc',
    };
  });
}

const columnDefaults: Record<ShiftReportColumn, GridColDef<ShiftReportRead>> = {
  [ShiftReportColumn.SID]: {
    field: ShiftReportColumn.SID,
    headerName: ShiftReportColumnDisplayMap[ShiftReportColumn.SID],
    sortable: true,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<ShiftReportRead, number, string>) =>
      DataGridCellLinkWrapper(ShiftReportSidCell(params), getShiftReportUrl(params.row.id)),
  },
  [ShiftReportColumn.GROUP]: {
    field: ShiftReportColumn.GROUP,
    headerName: ShiftReportColumnDisplayMap[ShiftReportColumn.GROUP],
    sortable: true,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<ShiftReportRead, string, string>) =>
      DataGridCellLinkWrapper(ShiftReportGroupCell(params), getShiftReportUrl(params.row.id)),
    valueGetter: (_, row) => row.group?.name || '',
  },
  [ShiftReportColumn.CREATED_BY]: {
    field: ShiftReportColumn.CREATED_BY,
    headerName: ShiftReportColumnDisplayMap[ShiftReportColumn.CREATED_BY],
    sortable: true,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<ShiftReportRead, string, string>) =>
      DataGridCellLinkWrapper(ShiftReportUserCell(params), getShiftReportUrl(params.row.id)),
    valueGetter: (_, row) => row.createdBy?.fullName || '',
  },
  [ShiftReportColumn.STATUS]: {
    field: ShiftReportColumn.STATUS,
    headerName: ShiftReportColumnDisplayMap[ShiftReportColumn.STATUS],
    sortable: true,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<ShiftReportRead, string, string>) =>
      DataGridCellLinkWrapper(ShiftReportStatusCell(params), getShiftReportUrl(params.row.id)),
    valueGetter: (_, row) => ShiftReportStatusDisplayMap[row.status] || '',
  },
  [ShiftReportColumn.DATE_RANGE]: {
    field: ShiftReportColumn.DATE_RANGE,
    headerName: ShiftReportColumnDisplayMap[ShiftReportColumn.DATE_RANGE],
    sortable: true,
    cellClassName: 'link',
    renderCell: (params: GridRenderCellParams<ShiftReportRead, string, string>) =>
      DataGridCellLinkWrapper(ShiftReportDateCell(params), getShiftReportUrl(params.row.id)),
    valueGetter: (_, row) => {
      const startTime = new Date(row.startTime).toDateString();
      const endTime = new Date(row.endTime).toDateString();
      return `${startTime} - ${endTime}`;
    },
  },
};

function ShiftReportListPage() {
  const { groupId } = useParams();
  const { page, setPage, pageSize, setPageSize } = usePaging();

  const { data: me } = useGetCurrentUserQuery();
  const shiftReportViewState = useAppSelector((state) => state.shiftReport.shiftReportViewState);
  const [sortModel, setSortModel] = useState<GridSortModel>(getGridModelFromSort(shiftReportViewState?.sort || []));
  const dispatch = useAppDispatch();

  const columns = useMemo(() => {
    const cols = shiftReportViewState.columns ? shiftReportViewState.columns : ShiftReportColumnDefaults;
    return cols
      .filter((c) => !c.hidden)
      .map((c) => ({
        ...columnDefaults[c.column],
        width: c.width,
      }));
  }, [shiftReportViewState.columns]);

  const getCandidateGroups = () =>
    shiftReportViewState?.candidateGroups?.length && shiftReportViewState.candidateGroups.length > 0
      ? shiftReportViewState?.candidateGroups
      : undefined;

  const getFilter = (view?: 'mine' | 'all') => {
    const usedView = view || shiftReportViewState.listView;
    if (usedView === 'all') {
      return undefined;
    }
    return `statusNot=${ShiftReportStatus.CLOSED}&statusNot=${ShiftReportStatus.CANCELED}`;
  };

  const [queryParams, setQueryParams] = useState<ShiftReportParams>({
    ancestorGroupId: Number(groupId),
    createdBy: shiftReportViewState.createdBy?.id,
    groupId: shiftReportViewState.group?.id,
    search: shiftReportViewState.search,
    status: shiftReportViewState.status,
    startDateGte: shiftReportViewState?.startDateGte,
    endDateLte: shiftReportViewState?.endDateLte,
    filter: getFilter(),
    candidateGroups: getCandidateGroups(),
    pageSize,
    pageNumber: page,
  });

  const { data, isLoading, error } = useGetShiftReportsQuery(queryParams);
  const canRequestShiftReport = (guardResult: GuardResult) =>
    guardResult.hasRole(UserRole.TENANT_ADMIN) || guardResult.hasPermission(PermissionType.SHIFT_REPORT_CREATE);

  useEffect(() => {
    if (shiftReportViewState) {
      const sort =
        shiftReportViewState.sort && shiftReportViewState.sort.length > 0
          ? shiftReportViewState.sort.map((s) => `${s.field}:${s.direction}`).join(',')
          : undefined;

      setQueryParams((prev) => ({
        ...prev,
        groupId: shiftReportViewState?.group?.id,
        createdBy: shiftReportViewState?.createdBy?.id,
        search: shiftReportViewState?.search,
        status: shiftReportViewState?.status,
        startDateGte: shiftReportViewState?.startDateGte,
        endDateLte: shiftReportViewState?.endDateLte,
        candidateGroups: getCandidateGroups(),
        sort,
      }));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [shiftReportViewState]);

  useEffect(() => {
    setQueryParams((prev) => ({
      ...prev,
      filter: getFilter(shiftReportViewState.listView),
    }));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [shiftReportViewState.listView]);

  const onTabSwitch = (view: 'mine' | 'all') => {
    setPage(0);
    dispatch(
      setShiftReportViewState({
        ...shiftReportViewState,
        listView: view,
        candidateGroups: getCandidateGroups(),
      })
    );
  };

  const handlePaginationChange = (updatedModel: GridPaginationModel) => {
    setPageSize(updatedModel.pageSize);
    setPage(updatedModel.page);
  };

  const handleSortModelChange = (newSortModel: GridSortModel) => {
    setSortModel(newSortModel);
    const newSort = getSortFromGridModel(newSortModel);
    dispatch(
      setShiftReportViewState({
        ...shiftReportViewState,
        sort: newSort,
      })
    );
  };

  const resetPageNumber = (): void => {
    handlePaginationChange({ page: 0, pageSize });
  };

  return (
    <ErrorGate error={error}>
      <PageTitle page="Shift reports" />
      <TabContext value="0">
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Tabs value={shiftReportViewState.listView} sx={{ width: 'fit-content' }}>
            <Tab label="Open" value="mine" onClick={() => onTabSwitch('mine')} />
            <Tab label="All" value="all" onClick={() => onTabSwitch('all')} />
          </Tabs>
          <Guard hasAccess={canRequestShiftReport}>
            <Box>
              <ResponsiveButton component={Link} to="add" variant="contained" size="large" endIcon={<AddIcon />}>
                Report shift
              </ResponsiveButton>
            </Box>
          </Guard>
        </Box>
        <ShiftReportChipFilter me={me} resetPageNumber={resetPageNumber} />
        <ShiftReportFilterBar groupId={Number(groupId)} resetPageNumber={resetPageNumber} />
        <TabPanel sx={{ px: 0, pt: 1, pb: 0 }} value="0">
          <Paper elevation={4}>
            <Box
              sx={{
                height: 'calc(100vh - 269px)',
                overflow: 'hidden',
                '@media (max-height: 600px)': {
                  height: '100%',
                },
              }}
            >
              <DataGrid
                rows={data?.content || []}
                columns={columns}
                rowCount={data?.total || 0}
                loading={isLoading}
                disableColumnMenu
                pagination
                paginationMode="server"
                paginationModel={{ page, pageSize }}
                onPaginationModelChange={handlePaginationChange}
                sortingMode="server"
                sortModel={sortModel}
                onSortModelChange={handleSortModelChange}
                disableRowSelectionOnClick
                slots={{
                  noRowsOverlay: NoRowsOverlay,
                }}
                slotProps={{
                  noRowsOverlay: {
                    title: 'No shift reports found',
                  },
                }}
              />
            </Box>
          </Paper>
        </TabPanel>
      </TabContext>
    </ErrorGate>
  );
}
export default ShiftReportListPage;
