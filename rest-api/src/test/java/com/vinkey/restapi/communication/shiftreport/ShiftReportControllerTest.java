package com.vinkey.restapi.communication.shiftreport;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.verify;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.vinkey.restapi.BaseControllerTest;
import com.vinkey.restapi.ResponseBodyMatcher;
import com.vinkey.restapi.WithMockJWTDetails;
import com.vinkey.restapi.common.file.FileService;
import com.vinkey.restapi.common.persistence.Deletable;
import com.vinkey.restapi.communication.shiftreport.builder.ShiftReportChangeBuilder;
import com.vinkey.restapi.communication.shiftreport.builder.ShiftReportCreateBuilder;
import com.vinkey.restapi.communication.shiftreport.builder.ShiftReportMother;
import com.vinkey.restapi.communication.shiftreport.builder.ShiftReportUpdateBuilder;
import com.vinkey.restapi.communication.shiftreport.dto.ShiftReportChange;
import com.vinkey.restapi.communication.shiftreport.dto.ShiftReportCreate;
import com.vinkey.restapi.communication.shiftreport.dto.ShiftReportRead;
import com.vinkey.restapi.communication.shiftreport.dto.ShiftReportUpdate;
import com.vinkey.restapi.flowable.process.ProcessInstanceCustom;
import com.vinkey.restapi.identityandaccess.auth.dto.JwtDetails;
import com.vinkey.restapi.identityandaccess.tenant.builder.TenantMother;
import com.vinkey.restapi.permittowork.workpermit.print.PrintSettings;
import com.vinkey.restapi.permittowork.workpermit.print.PrintSettingsService;
import com.vinkey.restapi.permittowork.workpermit.print.builder.PrintSettingsBuilder;
import java.io.OutputStream;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.ResultActions;

@WebMvcTest(ShiftReportController.class)
public class ShiftReportControllerTest extends BaseControllerTest {
  @MockBean private ShiftReportService shiftReportService;

  @MockBean private ShiftReportHistoryService shiftReportHistoryService;

  @MockBean private PrintSettingsService printSettingsService;

  @MockBean private FileService fileService;

  private ShiftReportMapper shiftReportMapper = ShiftReportMapper.INSTANCE;

  @Test
  @WithMockJWTDetails
  public void getShiftReport_withId_returnsOkAndReport() throws Exception {
    JwtDetails jwtDetails = getMockJwtDetails();
    ShiftReport shiftReport = ShiftReportMother.aSavedShiftReport().build();

    given(shiftReportService.getShiftReport(shiftReport.getId(), jwtDetails.getTenantId()))
        .willReturn(shiftReport);

    ResultActions result = mockMvc.perform(get("/v1/shift-reports/{id}", shiftReport.getId()));

    result
        .andExpect(status().isOk())
        .andExpect(
            ResponseBodyMatcher.responseBody()
                .containsObjectAsJson(shiftReportMapper.shiftReportToShiftReportRead(shiftReport)));
  }

  @Test
  @WithMockJWTDetails
  public void checkDeletable_withShiftReport_returnsOkAndTrue() throws Exception {
    ShiftReport shiftReport = ShiftReportMother.aSavedShiftReport().build();
    JwtDetails jwtDetails = getMockJwtDetails();
    Deletable deletable = new Deletable();
    deletable.setDeletable(true);

    given(shiftReportService.getShiftReport(shiftReport.getId(), jwtDetails.getTenantId()))
        .willReturn(shiftReport);
    given(shiftReportService.checkDeletable(shiftReport)).willReturn(true);

    ResultActions result =
        mockMvc.perform(get("/v1/shift-reports/{id}/deletable", shiftReport.getId()));

    result
        .andExpect(status().isOk())
        .andExpect(ResponseBodyMatcher.responseBody().containsObjectAsJson(deletable));
  }

  @Test
  @WithMockJWTDetails
  public void cancelShiftReport_withShiftReport_returnsNoContent() throws Exception {
    ShiftReport shiftReport = ShiftReportMother.aSavedShiftReport().build();
    JwtDetails jwtDetails = getMockJwtDetails();

    given(shiftReportService.getShiftReport(shiftReport.getId(), jwtDetails.getTenantId()))
        .willReturn(shiftReport);

    ResultActions result =
        mockMvc.perform(post("/v1/shift-reports/{id}/cancel", shiftReport.getId()));

    result.andExpect(status().isNoContent());
  }

  @Test
  @WithMockJWTDetails
  public void createShiftReport_withCreate_returnIsCreatedAndPermit() throws Exception {
    JwtDetails jwtDetails = getMockJwtDetails();
    ShiftReportCreate shiftReportCreate = ShiftReportCreateBuilder.aValidCreate().build();
    ShiftReport shiftReport =
        shiftReportMapper.shiftReportCreateToShiftReport(
            shiftReportCreate, jwtDetails.getTenantId());
    shiftReport.setTenant(TenantMother.aSavedTenant().build());
    ProcessInstanceCustom processInstanceCustom = new ProcessInstanceCustom();
    processInstanceCustom.setId("rndId");
    processInstanceCustom.setProcessInstanceId("strId");
    shiftReport.setProcessInstance(processInstanceCustom);
    ShiftReportRead shiftReportRead = shiftReportMapper.shiftReportToShiftReportRead(shiftReport);

    String json = mapper.writeValueAsString(shiftReportCreate);

    given(shiftReportService.createShiftReport(any(ShiftReport.class)))
        .willReturn(shiftReport.getId());
    given(shiftReportService.getShiftReport(shiftReport.getId(), shiftReport.getTenant().getId()))
        .willReturn(shiftReport);
    ResultActions result =
        mockMvc.perform(
            post("/v1/shift-reports").contentType(MediaType.APPLICATION_JSON).content(json));

    result
        .andExpect(status().isCreated())
        .andExpect(ResponseBodyMatcher.responseBody().containsObjectAsJson(shiftReportRead));
  }

  @Test
  @WithMockJWTDetails
  public void getShiftReports_withAllArgsNoFilter_returnsOk() throws Exception {
    JwtDetails jwtDetails = getMockJwtDetails();

    Long tenantId = jwtDetails.getTenantId();
    ShiftReport shiftReport1 = ShiftReportMother.aSavedShiftReport().build();
    ShiftReport shiftReport2 = ShiftReportMother.aSavedShiftReport().but().withId(88L).build();
    Long groupId = 1L;
    Long startDateLte = 1200L;
    Long startDateGte = 1200L;
    Long endDateGte = 1200L;
    Long endDateLte = 1200L;
    ShiftReportStatus status = ShiftReportStatus.OPEN;
    Long createdBy = 3L;
    String search = "sampleSearch";
    Long ancestorGroupId = 4L;
    List<String> candidateGroups = Arrays.asList("group1", "group2");
    Long pageSize = 10L;
    Long pageNumber = 1L;
    String filter = "tenantId=1";

    List.of(1L, 2L, 3L, 4L, 5L);

    List<ShiftReport> list = Arrays.asList(shiftReport1, shiftReport2);
    Pageable pageable =
        PageRequest.of(
            pageNumber.intValue(), pageSize.intValue(), Sort.by(ShiftReport_.ID).descending());
    Page<ShiftReport> page = new PageImpl<>(list, pageable, list.size());

    given(
            shiftReportService.getShiftReportPage(
                tenantId,
                groupId,
                status,
                startDateGte,
                startDateLte,
                endDateGte,
                endDateLte,
                ancestorGroupId,
                createdBy,
                candidateGroups,
                search,
                filter,
                null,
                pageSize,
                pageNumber))
        .willReturn(page);

    ResultActions result =
        mockMvc.perform(
            get("/v1/shift-reports")
                .queryParam("status", status.name())
                .param("groupId", groupId.toString())
                .queryParam("createdBy", createdBy.toString())
                .queryParam("search", search)
                .queryParam("ancestorGroupId", ancestorGroupId.toString())
                .queryParam(
                    "candidateGroups",
                    candidateGroups.stream().map(Object::toString).collect(Collectors.joining(",")))
                .queryParam("pageSize", pageSize.toString())
                .queryParam("pageNumber", pageNumber.toString())
                .param("filter", filter)
                .param("startDateLte", startDateLte.toString())
                .param("startDateGte", startDateGte.toString())
                .param("endDateGte", endDateGte.toString())
                .param("endDateLte", endDateLte.toString())
                .contentType(MediaType.APPLICATION_JSON));

    result
        .andExpect(status().isOk())
        .andExpect(
            ResponseBodyMatcher.responseBody()
                .containsObjectAsJson(
                    shiftReportMapper.paginatedShiftReportsToPaginatedShiftReportListReads(page)));
  }

  @Test
  @WithMockJWTDetails
  public void updateShiftReport_withIdandUpdate_returnsOk() throws Exception {
    JwtDetails jwtDetails = getMockJwtDetails();
    ShiftReport shiftReport = ShiftReportMother.aSavedShiftReport().build();
    ShiftReportUpdate shiftReportUpdate = ShiftReportUpdateBuilder.aValidUpdate().build();
    shiftReportMapper.updateShiftReportFromShiftReportUpdate(shiftReport, shiftReportUpdate);

    String json = mapper.writeValueAsString(shiftReportUpdate);

    given(shiftReportService.getShiftReport(shiftReport.getId(), jwtDetails.getTenantId()))
        .willReturn(shiftReport);
    given(shiftReportService.updateShiftReport(shiftReport)).willReturn(shiftReport);

    ResultActions result =
        mockMvc.perform(
            put("/v1/shift-reports/{id}", shiftReport.getId())
                .contentType(MediaType.APPLICATION_JSON)
                .content(json));

    result
        .andExpect(status().isOk())
        .andExpect(
            ResponseBodyMatcher.responseBody()
                .containsObjectAsJson(shiftReportMapper.shiftReportToShiftReportRead(shiftReport)));
  }

  @Test
  @WithMockJWTDetails
  public void cancelShiftReport_withId_returnsNoContent() throws Exception {
    JwtDetails jwtDetails = getMockJwtDetails();
    ShiftReport shiftReport = ShiftReportMother.aSavedShiftReport().build();

    given(shiftReportService.getShiftReport(shiftReport.getId(), jwtDetails.getTenantId()))
        .willReturn(shiftReport);

    ResultActions result = mockMvc.perform(delete("/v1/shift-reports/{id}", shiftReport.getId()));

    result.andExpect(status().isNoContent());
  }

  @Test
  @WithMockJWTDetails
  public void generateShiftReportPdf_withId_returnsOk() throws Exception {
    JwtDetails jwtDetails = getMockJwtDetails();
    ShiftReport shiftReport = ShiftReportMother.aSavedShiftReport().build();
    PrintSettings printSettings = PrintSettingsBuilder.aValidPrintSettings().build();

    given(shiftReportService.getShiftReport(shiftReport.getId(), jwtDetails.getTenantId()))
        .willReturn(shiftReport);
    given(printSettingsService.getPrintSettingsById(jwtDetails.getTenantId()))
        .willReturn(printSettings);

    ResultActions result =
        mockMvc.perform(
            get("/v1/shift-reports/{id}/pdf", shiftReport.getId())
                .param("timeZone", "UTC")
                .contentType(MediaType.APPLICATION_PDF_VALUE));

    verify(shiftReportService)
        .generateShiftReportPdf(eq(shiftReport), eq("UTC"), any(OutputStream.class));
    result.andExpect(status().isOk());
  }

  @Test
  @WithMockJWTDetails
  public void getShiftReportHistory_withId_returnsOk() throws Exception {
    JwtDetails jwtDetails = getMockJwtDetails();
    ShiftReportChange shiftReportChange = ShiftReportChangeBuilder.aValidChange().build();
    List<ShiftReportChange> list = List.of(shiftReportChange);

    given(shiftReportHistoryService.getHistory(1L, jwtDetails.getTenantId())).willReturn(list);

    ResultActions result = mockMvc.perform(get("/v1/shift-reports/{id}/history", 1L));

    result
        .andExpect(status().isOk())
        .andExpect(ResponseBodyMatcher.responseBody().containsObjectAsJson(list));
  }
}
