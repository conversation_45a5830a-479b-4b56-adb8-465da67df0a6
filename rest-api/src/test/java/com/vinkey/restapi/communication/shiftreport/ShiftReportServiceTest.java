package com.vinkey.restapi.communication.shiftreport;

import static org.assertj.core.api.BDDAssertions.then;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import com.vinkey.restapi.common.persistence.filter.SpecFilterCriteria;
import com.vinkey.restapi.communication.shiftreport.builder.ShiftReportMother;
import com.vinkey.restapi.communication.shiftreport.notification.ShiftReportNotificationQueuer;
import com.vinkey.restapi.communication.shiftreport.notification.event.ShiftReportCanceledEvent;
import com.vinkey.restapi.communication.shiftreport.notification.event.ShiftReportHandoverStartedEvent;
import com.vinkey.restapi.communication.shiftreport.subtopic.ShiftReportSubTopic;
import com.vinkey.restapi.communication.shiftreport.subtopic.ShiftReportSubTopicService;
import com.vinkey.restapi.communication.shiftreport.subtopic.builder.ShiftReportSubTopicBuilder;
import com.vinkey.restapi.flowable.process.ProcessInstanceCustom;
import com.vinkey.restapi.flowable.task.TaskService;
import com.vinkey.restapi.flowable.task.history.TaskHistory;
import com.vinkey.restapi.identityandaccess.group.Group;
import com.vinkey.restapi.identityandaccess.group.GroupRepository;
import com.vinkey.restapi.identityandaccess.group.builder.GroupMother;
import java.io.ByteArrayOutputStream;
import java.sql.Timestamp;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.engine.runtime.ProcessInstanceBuilder;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.thymeleaf.ITemplateEngine;
import org.thymeleaf.context.IContext;

@ExtendWith(MockitoExtension.class)
public class ShiftReportServiceTest {
  @InjectMocks private ShiftReportService shiftReportService;
  @Mock private ShiftReportRepository shiftReportRepository;
  @Mock private ShiftReportNotificationQueuer shiftReportNotificationService;
  @Mock private ApplicationEventPublisher applicationEventPublisher;
  @Mock private GroupRepository groupRepository;
  @Mock private RuntimeService runtimeService;
  @Mock private TaskService taskServiceCustom;
  @Mock private ITemplateEngine templateEngine;
  @Mock private ShiftReportSubTopicService shiftReportSubTopicService;

  public void compareShiftReports(ShiftReport expected, ShiftReport result) {
    then(result.getCreatedBy().getId()).isEqualTo(expected.getCreatedBy().getId());
    then(result.getCreationDate()).isEqualTo(expected.getCreationDate());
    then(result.getEndTime()).isEqualTo(expected.getEndTime());
    then(result.getGroup().getId()).isEqualTo(expected.getGroup().getId());
    then(result.getModifiedDate()).isEqualTo(expected.getModifiedDate());
    then(result.getModifiedBy().getId()).isEqualTo(expected.getModifiedBy().getId());
    then(result.getId()).isEqualTo(expected.getId());
    then(result.getLocked()).isEqualTo(expected.getLocked());
    then(result.getSid()).isEqualTo(expected.getSid());
    then(result.getStartTime()).isEqualTo(expected.getStartTime());
    then(result.getStatus()).isEqualTo(expected.getStatus());
    then(result.getTenant().getId()).isEqualTo(expected.getTenant().getId());
  }

  @Test
  public void createShiftReport_withShiftReport_withTra_returnsShiftReport() {
    ProcessInstanceBuilder instanceBuilder = mock(ProcessInstanceBuilder.class);
    ProcessInstance processInstance = mock(ProcessInstance.class);
    ShiftReport shiftReport = ShiftReportMother.aSavedShiftReport().build();
    Group group = GroupMother.aSavedGroup().build();

    given(processInstance.getProcessInstanceId()).willReturn("123");
    given(processInstance.getId()).willReturn("321");
    given(runtimeService.createProcessInstanceBuilder()).willReturn(instanceBuilder);
    given(instanceBuilder.processDefinitionKey("shiftReportProcess")).willReturn(instanceBuilder);
    given(instanceBuilder.tenantId("1")).willReturn(instanceBuilder);
    given(instanceBuilder.start()).willReturn(processInstance);
    given(
            groupRepository.findByIdAndTenantId(
                shiftReport.getGroup().getId(), shiftReport.getTenant().getId()))
        .willReturn(Optional.of(group));

    Long result = shiftReportService.createShiftReport(shiftReport);

    verify(shiftReportRepository).save(shiftReport);
    then(result).isEqualTo(shiftReport.getId());
  }

  @Test
  public void getShiftReport_withValidIds_returnsShiftReport() {
    ShiftReport shiftReport = ShiftReportMother.aSavedShiftReport().build();

    given(
            shiftReportRepository.findByIdAndTenantId(
                shiftReport.getId(), shiftReport.getTenant().getId()))
        .willReturn(Optional.of(shiftReport));

    ShiftReport result =
        shiftReportService.getShiftReport(shiftReport.getId(), shiftReport.getTenant().getId());

    compareShiftReports(shiftReport, result);
  }

  @Test
  public void getShiftReport_withInvalidIds_returnsNoSuchElementException() {
    Long id = 1L;
    ShiftReportNotFoundException exception =
        assertThrows(
            ShiftReportNotFoundException.class, () -> shiftReportService.getShiftReport(1L, 2L));

    then(exception.getMessage()).isEqualTo("ShiftReport not found with id: " + id);
  }

  @Test
  public void retrieveFilterKeyValue_withKeyAndFilter_returnsLong() {
    SpecFilterCriteria criteria1 = mock(SpecFilterCriteria.class);
    SpecFilterCriteria criteria2 = mock(SpecFilterCriteria.class);

    List<SpecFilterCriteria> decodedFilter = Arrays.asList(criteria1, criteria2);

    given(criteria1.getKey()).willReturn("key1");
    given(criteria1.getValue()).willReturn("123");
    given(criteria2.getKey()).willReturn("key1");
    given(criteria2.getValue()).willReturn("456");

    Long result = shiftReportService.retrieveFilterKeyValue(decodedFilter, "key1");

    then(result).isEqualTo(123L);
  }

  @Test
  public void retrieveFilterKeyValue_withourFilter_returnsNull() {
    Long result = shiftReportService.retrieveFilterKeyValue(null, "key1");

    then(result).isNull();
    ;
  }

  @Test
  public void retrieveFilterKeyValue_withWrongKeyAndFilter_returnsNull() {
    SpecFilterCriteria criteria1 = mock(SpecFilterCriteria.class);
    SpecFilterCriteria criteria2 = mock(SpecFilterCriteria.class);

    List<SpecFilterCriteria> decodedFilter = Arrays.asList(criteria1, criteria2);

    given(criteria1.getKey()).willReturn("key1");
    given(criteria2.getKey()).willReturn("key1");

    Long result = shiftReportService.retrieveFilterKeyValue(decodedFilter, "key2");

    then(result).isNull();
  }

  @Test
  public void getShiftReports_withValidArgs_returnsPage() {
    ShiftReport shiftReport1 = ShiftReportMother.aSavedShiftReport().build();
    ShiftReport shiftReport2 = ShiftReportMother.aSavedShiftReport().but().withId(88L).build();
    Long tenantId = 1L;
    Long groupId = 4L;
    ShiftReportStatus status = ShiftReportStatus.OPEN;
    Long createdBy = 8L;
    List<String> candidateGroups = new ArrayList<>();
    candidateGroups.add("Shell Group");
    candidateGroups.add("Esso Group");
    Long ancestorGroupId = 55L;
    Long startDateGte = 123123L;
    Long startDateLte = 234234L;
    Long endDateGte = 200L;
    Long endDateLte = 200L;
    String search = "Lifting permit";
    Long pageSize = 10L;
    Long pageNumber = 0L;
    String filter = null;
    Set<Long> workMethodIds = new HashSet<>();
    workMethodIds.add(2L);
    workMethodIds.add(3L);
    workMethodIds.add(5L);

    List<ShiftReport> list = Arrays.asList(shiftReport1, shiftReport2);
    Pageable pageable =
        PageRequest.of(
            pageNumber.intValue(), pageSize.intValue(), Sort.by(ShiftReport_.ID).descending());
    Page<ShiftReport> page = new PageImpl<>(list, pageable, list.size());

    given(shiftReportRepository.findAll(any(Specification.class), any(Pageable.class)))
        .willReturn(page);

    Page<ShiftReport> result =
        shiftReportService.getShiftReportPage(
            tenantId,
            groupId,
            status,
            startDateGte,
            startDateLte,
            endDateGte,
            endDateLte,
            ancestorGroupId,
            createdBy,
            candidateGroups,
            search,
            filter,
            null,
            pageSize,
            pageNumber);

    then(result.getNumber()).isEqualTo(page.getNumber());
    then(result.getNumberOfElements()).isEqualTo(page.getNumberOfElements());
    then(result.getSize()).isEqualTo(page.getSize());
    then(result.getSort()).isEqualTo(page.getSort());
    then(result.getTotalElements()).isEqualTo(page.getTotalElements());
    then(result.getTotalPages()).isEqualTo(page.getTotalPages());
    compareShiftReports(result.getContent().get(0), page.getContent().get(0));
    compareShiftReports(result.getContent().get(1), page.getContent().get(1));
  }

  @Test
  public void getShiftReports_withNullPageCreds_returnsError() {
    ShiftReport shiftReport1 = ShiftReportMother.aSavedShiftReport().build();
    ShiftReport shiftReport2 = ShiftReportMother.aSavedShiftReport().but().withId(88L).build();
    Long tenantId = null;
    Long groupId = 4L;
    ShiftReportStatus status = ShiftReportStatus.OPEN;
    Long createdBy = 8L;
    List<String> candidateGroups = new ArrayList<>();
    candidateGroups.add("Shell Group");
    candidateGroups.add("Esso Group");
    Long ancestorGroupId = 55L;
    Long startDateGte = 123123L;
    Long startDateLte = 234234L;
    Long endDateGte = 200L;
    Long endDateLte = 200L;
    String search = "Lifting permit";
    Long pageSize = null;
    Long pageNumber = null;
    String filter = "tenantId=1";
    Set<Long> workMethodIds = new HashSet<>();
    workMethodIds.add(2L);
    workMethodIds.add(3L);
    workMethodIds.add(5L);

    List<ShiftReport> list = Arrays.asList(shiftReport1, shiftReport2);
    Page<ShiftReport> page = new PageImpl<>(list);

    given(shiftReportRepository.findAll(any(Specification.class), any(Pageable.class)))
        .willReturn(page);

    Page<ShiftReport> result =
        shiftReportService.getShiftReportPage(
            tenantId,
            groupId,
            status,
            startDateGte,
            startDateLte,
            endDateGte,
            endDateLte,
            ancestorGroupId,
            createdBy,
            candidateGroups,
            search,
            filter,
            null,
            pageSize,
            pageNumber);

    then(result.getNumber()).isEqualTo(page.getNumber());
    then(result.getNumberOfElements()).isEqualTo(page.getNumberOfElements());
    then(result.getSize()).isEqualTo(page.getSize());
    then(result.getSort()).isEqualTo(page.getSort());
    then(result.getTotalElements()).isEqualTo(page.getTotalElements());
    then(result.getTotalPages()).isEqualTo(page.getTotalPages());
    compareShiftReports(result.getContent().get(0), page.getContent().get(0));
    compareShiftReports(result.getContent().get(1), page.getContent().get(1));
  }

  @Test
  public void updateShiftReport_withLockedShiftReport_throwsLockedException() {
    ShiftReport shiftReport = ShiftReportMother.aSavedShiftReport().build();
    shiftReport.setLocked(true);

    assertThrows(
        ShiftReportLockedException.class, () -> shiftReportService.updateShiftReport(shiftReport));
  }

  @Test
  public void updateShiftReport_withUnlockedAndDifferentRiskCategory_updatesShiftReport() {
    ShiftReport shiftReport = ShiftReportMother.aSavedShiftReport().build();

    given(
            shiftReportRepository.findByIdAndTenantId(
                shiftReport.getId(), shiftReport.getTenant().getId()))
        .willReturn(Optional.of(shiftReport));

    given(shiftReportRepository.save(shiftReport)).willReturn(shiftReport);

    ShiftReport result = shiftReportService.updateShiftReport(shiftReport);

    compareShiftReports(shiftReport, result);
  }

  @Test
  public void cancelShiftReport_withShiftReport_cancelsShiftReport() {
    ShiftReport shiftReport = ShiftReportMother.aSavedShiftReport().build();
    ProcessInstanceCustom processInstanceCustom = new ProcessInstanceCustom();
    processInstanceCustom.setId("122");
    shiftReport.setProcessInstance(processInstanceCustom);
    shiftReport.setLocked(true);
    shiftReportService.cancelShiftReport(shiftReport);

    verify(shiftReportRepository).save(shiftReport);
    verify(runtimeService)
        .deleteProcessInstance(
            shiftReport.getProcessInstance().getId(), "Shift report canceled by user");

    verify(applicationEventPublisher)
        .publishEvent(new ShiftReportCanceledEvent(shiftReport.getId()));
  }

  @Test
  public void cancelShiftReport_withShiftReportNoProcesssInstance_cancelsShiftReport() {
    ShiftReport shiftReport = ShiftReportMother.aSavedShiftReport().build();
    shiftReport.setLocked(true);
    shiftReportService.cancelShiftReport(shiftReport);

    verify(shiftReportRepository).save(shiftReport);

    verify(applicationEventPublisher)
        .publishEvent(new ShiftReportCanceledEvent(shiftReport.getId()));
  }

  @Test
  public void startHandoverShiftReport_withShiftReport_changesStatus() {
    ShiftReport shiftReport = ShiftReportMother.aSavedShiftReport().build();

    given(
            shiftReportRepository.findByIdAndTenantId(
                shiftReport.getId(), shiftReport.getTenant().getId()))
        .willReturn(Optional.of(shiftReport));
    shiftReportService.startHandoverShiftReport(
        shiftReport.getId(), shiftReport.getTenant().getId());
    verify(shiftReportRepository).save(shiftReport);

    verify(applicationEventPublisher)
        .publishEvent(new ShiftReportHandoverStartedEvent(shiftReport.getId()));
  }

  @Test
  public void closeShiftReport_withShiftReport_closesShiftReport() {
    ShiftReport shiftReport = ShiftReportMother.aSavedShiftReport().build();
    given(
            shiftReportRepository.findByIdAndTenantId(
                shiftReport.getId(), shiftReport.getTenant().getId()))
        .willReturn(Optional.of(shiftReport));

    shiftReportService.closeShifReport(shiftReport.getId(), shiftReport.getTenant().getId());

    verify(shiftReportRepository).save(shiftReport);
    then(shiftReport.getStatus()).isEqualTo(ShiftReportStatus.CLOSED);
  }

  @Test
  public void lockShiftReport_withShiftReportIds_locksPermit() {
    ShiftReport shiftReport = ShiftReportMother.aSavedShiftReport().build();

    given(
            shiftReportRepository.findByIdAndTenantId(
                shiftReport.getId(), shiftReport.getTenant().getId()))
        .willReturn(Optional.of(shiftReport));

    shiftReportService.lockShiftReport(shiftReport.getId(), shiftReport.getTenant().getId());

    verify(shiftReportRepository).save(shiftReport);
    then(shiftReport.getLocked()).isTrue();
  }

  @Test
  public void generateShiftReportPdf_withArgs_returnsOutput() {
    Set<ShiftReportSubTopic> subTopics = new HashSet<>();
    subTopics.add(ShiftReportSubTopicBuilder.aValidSubTopic().build());
    ShiftReport shiftReport =
        ShiftReportMother.aSavedShiftReport().withShiftReportSubTopic(subTopics).build();
    ProcessInstanceCustom processInstanceCustom = new ProcessInstanceCustom();
    processInstanceCustom.setId("123");
    shiftReport.setProcessInstance(processInstanceCustom);
    String timeZone = "UTC";
    ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

    Timestamp currentDate = Timestamp.from(Instant.now());
    TaskHistory task1 = new TaskHistory();
    task1.setName("Process feedback");
    task1.setTaskDefinitionKey("key1");
    TaskHistory task2 = new TaskHistory();
    task2.setName("Process feedback");
    task2.setTaskDefinitionKey("key1");
    task2.setEndTime(currentDate);
    TaskHistory task3 = new TaskHistory();
    task3.setName("Revoke");
    task3.setTaskDefinitionKey("key3");
    task3.setEndTime(currentDate);
    TaskHistory task4 = new TaskHistory();
    task4.setName("DE-LOTOTO");
    task4.setTaskDefinitionKey("key4");
    task4.setEndTime(currentDate);
    TaskHistory task5 = new TaskHistory();
    task5.setName("Start closing");
    task5.setTaskDefinitionKey("key5");
    task5.setEndTime(currentDate);
    TaskHistory task6 = new TaskHistory();
    task6.setName("Issue");
    task6.setTaskDefinitionKey("key6");
    task6.setEndTime(currentDate);
    TaskHistory task7 = new TaskHistory();
    task7.setName("Validate");
    task7.setTaskDefinitionKey("key7");
    task7.setEndTime(currentDate);
    List<TaskHistory> historyTasks = List.of(task1, task2, task3, task4, task5, task6, task7);

    given(taskServiceCustom.getHistory(any(String.class), any(Long.class)))
        .willReturn(historyTasks);
    given(templateEngine.process(eq("pdf/shift-report.html"), any(IContext.class)))
        .willReturn("Mocked HTML");

    shiftReportService.generateShiftReportPdf(shiftReport, timeZone, outputStream);

    verify(taskServiceCustom, times(1)).getHistory(any(String.class), any(Long.class));

    byte[] generatedPdfBytes = outputStream.toByteArray();
    then(generatedPdfBytes.length).isGreaterThan(0);
  }

  @Test
  public void checkDeletable_withShiftReport_returnsBool() {
    ShiftReport shiftReport = ShiftReportMother.aSavedShiftReport().build();

    given(shiftReportRepository.existsAnyRelationsById(shiftReport.getId())).willReturn(false);

    Boolean result = shiftReportService.checkDeletable(shiftReport);

    then(result).isTrue();
  }

  @Test
  public void deleteShiftReport_withDeletableShiftreport_deletes() {
    ShiftReport shiftReport = ShiftReportMother.aSavedShiftReport().build();

    given(shiftReportRepository.existsAnyRelationsById(shiftReport.getId())).willReturn(false);

    shiftReportService.deleteShiftReport(shiftReport);

    verify(shiftReportRepository).delete(shiftReport);
  }

  @Test
  public void deleteShiftReport_withNotDeletableShiftreport_ThrowsException() {
    ShiftReport shiftReport = ShiftReportMother.aSavedShiftReport().build();

    given(shiftReportRepository.existsAnyRelationsById(shiftReport.getId())).willReturn(true);

    ShiftReportDeletionException error =
        assertThrows(
            ShiftReportDeletionException.class,
            () -> shiftReportService.deleteShiftReport(shiftReport));

    then(error.getMessage())
        .isEqualTo("ShiftReport with id " + shiftReport.getId() + " is not deletable");
  }
}
