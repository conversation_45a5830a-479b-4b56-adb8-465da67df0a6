package com.vinkey.restapi.managementofchange.checklist.answer;

import com.openhtmltopdf.pdfboxout.PdfRendererBuilder;
import com.vinkey.restapi.common.persistence.filter.FilterDecoder;
import com.vinkey.restapi.common.persistence.filter.SpecFilterCriteria;
import com.vinkey.restapi.flowable.process.ProcessInstanceCustom;
import com.vinkey.restapi.flowable.task.TaskService;
import com.vinkey.restapi.flowable.task.history.TaskHistory;
import com.vinkey.restapi.managementofchange.checklist.answer.exception.ChecklistAnswerLockedException;
import com.vinkey.restapi.managementofchange.checklist.answer.exception.ChecklistAnswerNotDeletableException;
import com.vinkey.restapi.managementofchange.checklist.answer.exception.ChecklistAnswerNotFoundException;
import com.vinkey.restapi.managementofchange.checklist.answer.notification.event.IssueResolvedEvent;
import com.vinkey.restapi.managementofchange.checklist.answer.notification.event.IssueUpdatedEvent;
import com.vinkey.restapi.permittowork.workpermit.PDFException;
import java.io.IOException;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Queue;
import java.util.stream.Collectors;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.runtime.ProcessInstance;
import org.jsoup.Jsoup;
import org.jsoup.helper.W3CDom;
import org.jsoup.nodes.Document;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.security.access.prepost.PostAuthorize;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thymeleaf.ITemplateEngine;
import org.thymeleaf.context.Context;

@Service
public class ChecklistAnswerService {

  @Value("${pagination.defaultpageSize}")
  private Long pageSize = 20L;

  @Value("${pagination.defaultpageNumber}")
  private Long pageNumber = 0L;

  private final ChecklistAnswerRepository checklistAnswerRepository;
  private final RuntimeService runtimeService;
  private final ApplicationEventPublisher applicationEventPublisher;
  private final ITemplateEngine templateEngine;
  private final TaskService taskServiceCustom;

  public ChecklistAnswerService(
      ChecklistAnswerRepository checklistAnswerRepository,
      RuntimeService runtimeService,
      ApplicationEventPublisher applicationEventPublisher,
      ITemplateEngine templateEngine,
      TaskService taskServiceCustom) {
    this.checklistAnswerRepository = checklistAnswerRepository;
    this.runtimeService = runtimeService;
    this.applicationEventPublisher = applicationEventPublisher;
    this.templateEngine = templateEngine;
    this.taskServiceCustom = taskServiceCustom;
  }

  @PostAuthorize(
      """
        hasRole('TENANT_ADMIN')
        OR @membershipService.hasPrivilege(returnObject.group.id, authentication.principal.userId, 'CHANGE_READ')
      """)
  public ChecklistAnswer read(Long id, Long tenantId) {
    return checklistAnswerRepository
        .findByIdAndTenantId(id, tenantId)
        .orElseThrow(() -> new ChecklistAnswerNotFoundException());
  }

  @PreAuthorize(
      """
        hasRole('TENANT_ADMIN')
        or @membershipService.hasPrivilege(#groupId, authentication.principal.userId, 'CHANGE_READ')
        or @membershipService.hasPrivilege(#ancestorId, authentication.principal.userId, 'CHANGE_READ')
        or @membershipService.hasPrivilege(
          @filterSpELHelper.retrieveLongs(T(com.vinkey.restapi.common.persistence.filter.FilterDecoder).decode(#filter), "groupId"),
          authentication.principal.userId,
          'CHANGE_READ'
        )
        or @membershipService.hasPrivilege(
          @filterSpELHelper.retrieveLongs(T(com.vinkey.restapi.common.persistence.filter.FilterDecoder).decode(#filter), "ancestorId"),
          authentication.principal.userId,
          'CHANGE_READ'
        )
      """)
  public Page<ChecklistAnswer> readAll(
      Long tenantId,
      IssueStatus status,
      List<String> candidateGroups,
      List<Long> candidateUsers,
      Long createdBy,
      Long groupId,
      Long ancestorId,
      Long changeId,
      String filter,
      String search,
      String doneBefore,
      Long assignee,
      String sort,
      Long pageNumber,
      Long pageSize) {
    pageSize = pageSize == null ? this.pageSize : pageSize;
    pageNumber = pageNumber == null ? this.pageNumber : pageNumber;

    Sort sortBy = Sort.by(Sort.Direction.DESC, ChecklistAnswer_.ID);
    if (sort != null && !sort.isEmpty()) {
      List<Sort.Order> sortOrders =
          com.vinkey.restapi.common.persistence.sort.SortDecoder.decode(
                  sort, ChecklistAnswerSortBy.class)
              .stream()
              .toList();
      if (!sortOrders.isEmpty()) {
        sortBy = Sort.by(sortOrders);
      }
    }

    Pageable pageable = PageRequest.of(pageNumber.intValue(), pageSize.intValue(), sortBy);
    ChecklistAnswerSpecificationBuilder builder =
        new ChecklistAnswerSpecificationBuilder()
            .tenant(tenantId)
            .status(status)
            .candidateGroups(candidateGroups)
            .candidateUsers(candidateUsers)
            .createdBy(createdBy)
            .group(groupId)
            .ancestorGroup(ancestorId)
            .change(changeId)
            .search(search)
            .doneBefore(doneBefore)
            .assignee(assignee)
            .withIssue(true);
    if (filter != null) {
      Queue<SpecFilterCriteria> filterSpecs = FilterDecoder.decode(filter);
      while (!filterSpecs.isEmpty()) {
        builder.withFilter(filterSpecs.poll());
      }
    }
    return checklistAnswerRepository.findAll(builder.build(), pageable);
  }

  @PreAuthorize(
      """
        hasRole('TENANT_ADMIN')
        or @membershipService.hasPrivilege(#checklistAnswer.getGroup().getId(), authentication.principal.userId, 'CHANGE_CREATE')
      """)
  @Transactional
  public ChecklistAnswer create(ChecklistAnswer checklistAnswer) {

    attachProcessToIssue(checklistAnswer);

    checklistAnswer.setStatus(IssueStatus.ACTIVE);
    checklistAnswer.setLocked(false);

    checklistAnswerRepository.saveAndFlush(checklistAnswer);

    runtimeService.setVariables(
        checklistAnswer.getProcessInstance().getId(), getIssueVariables(checklistAnswer));

    return checklistAnswerRepository.save(checklistAnswer);
  }

  @PreAuthorize(
      "hasRole('TENANT_ADMIN') or @membershipService.hasPrivilege(#checklistAnswer.getGroup().getId(), authentication.principal.userId, 'CHANGE_UPDATE')")
  @Transactional
  public Long update(ChecklistAnswer checklistAnswer) {
    if (Boolean.TRUE.equals(checklistAnswer.getLocked())) {
      throw new ChecklistAnswerLockedException();
    }

    ChecklistAnswer oldChecklistAnswer =
        read(checklistAnswer.getId(), checklistAnswer.getTenant().getId());
    if (!Objects.equals(checklistAnswer.getDescription(), oldChecklistAnswer.getDescription())) {
      runtimeService.setVariable(
          checklistAnswer.getProcessInstance().getId(), "name", checklistAnswer.getDescription());
    }

    checklistAnswerRepository.save(checklistAnswer);
    applicationEventPublisher.publishEvent(new IssueUpdatedEvent(checklistAnswer.getId()));
    return checklistAnswer.getId();
  }

  @PreAuthorize(
      "hasRole('TENANT_ADMIN') or @membershipService.hasPrivilege(#checklistAnswer.getGroup().getId(), authentication.principal.userId, 'CHANGE_DELETE')")
  public Boolean checkDeletable(ChecklistAnswer checklistAnswer) {
    return !checklistAnswerRepository.existsAnyRelationsById(checklistAnswer.getId());
  }

  @PreAuthorize(
      "hasRole('TENANT_ADMIN') or @membershipService.hasPrivilege(#checklistAnswer.getGroup().getId(), authentication.principal.userId, 'CHANGE_DELETE')")
  public void delete(ChecklistAnswer checklistAnswer) {
    if (!checkDeletable(checklistAnswer)) {
      throw new ChecklistAnswerNotDeletableException(checklistAnswer.getId());
    }
    checklistAnswerRepository.delete(checklistAnswer);
  }

  @Transactional
  public void resolve(Long id, Long tenantId) {
    ChecklistAnswer checklistAnswer = read(id, tenantId);
    checklistAnswer.setStatus(IssueStatus.RESOLVED);
    checklistAnswerRepository.save(checklistAnswer);
    applicationEventPublisher.publishEvent(new IssueResolvedEvent(id));
  }

  @Transactional
  public void lock(Long id, Long tenantId) {
    ChecklistAnswer checklistAnswer = read(id, tenantId);
    checklistAnswer.setLocked(true);
    checklistAnswerRepository.save(checklistAnswer);
  }

  @PreAuthorize(
      """
      hasRole('TENANT_ADMIN') or @membershipService.hasPrivilege(#checklistAnswer.getGroup().getId(), authentication.principal.userId, 'CHANGE_READ')
    """)
  public void generateIssuePdf(ChecklistAnswer checklistAnswer, String timeZone, OutputStream out) {
    Context context = generatePdfContext(checklistAnswer, timeZone);
    String html = templateEngine.process("pdf/checklist-issue.html", context);
    Document document = Jsoup.parse(html, "UTF-8");
    document.outputSettings().syntax(Document.OutputSettings.Syntax.xml);

    // take the copy of the stream and re-write it to an InputStream
    // try-with-resources here
    // putting the try block outside the Thread will cause the
    // PipedOutputStream resource to close before the Runnable finishes
    try (out) {
      PdfRendererBuilder builder = new PdfRendererBuilder();

      builder.withUri("issue.pdf");
      builder.toStream(out);
      builder.withW3cDocument(new W3CDom().fromJsoup(document), "/");
      builder.run();
    } catch (IOException e) {
      // logging and exception handling should go here
      e.printStackTrace();
      throw new PDFException();
    }
  }

  private Context generatePdfContext(ChecklistAnswer checklistAnswer, String timeZone) {
    List<TaskHistory> historyTasks =
        taskServiceCustom.getHistory(
            checklistAnswer.getProcessInstance().getId(), checklistAnswer.getTenant().getId());
    Map<String, List<TaskHistory>> multiValueMap = new HashMap<>();
    historyTasks.stream()
        .forEach(
            task -> {
              if (multiValueMap.containsKey(task.getTaskDefinitionKey()))
                multiValueMap.get(task.getTaskDefinitionKey()).add(task);
              else
                multiValueMap.put(
                    task.getTaskDefinitionKey(), new ArrayList<>(Arrays.asList(task)));
            });

    List<TaskHistory> taskOnly =
        multiValueMap.values().stream()
            .map(taskList -> taskList.stream().filter(task -> task.getEndTime() != null).toList())
            .filter(taskList -> !taskList.isEmpty())
            .map(
                tasks ->
                    Collections.max(
                        tasks, Comparator.comparing(task -> task.getEndTime().getTime())))
            .collect(Collectors.toList());

    if (!taskOnly.isEmpty())
      Collections.sort(taskOnly, Comparator.comparing(TaskHistory::getEndTime));

    Context context = new Context();
    context.setVariable("answer", checklistAnswer);
    context.setVariable("timeZone", timeZone);
    context.setVariable("tasks", taskOnly);

    return context;
  }

  private void attachProcessToIssue(ChecklistAnswer answer) {
    ProcessInstance issueProcessInstance =
        runtimeService.startProcessInstanceByKeyAndTenantId(
            "issueProcess", answer.getTenant().getId().toString());

    ProcessInstanceCustom processInstanceCustom = new ProcessInstanceCustom();
    processInstanceCustom.setId(issueProcessInstance.getId());
    processInstanceCustom.setProcessInstanceId(issueProcessInstance.getProcessInstanceId());
    answer.setProcessInstance(processInstanceCustom);
  }

  private Map<String, Object> getIssueVariables(ChecklistAnswer checklistAnswer) {
    Map<String, Object> variables = new HashMap<String, Object>();
    variables.put("id", checklistAnswer.getId());
    variables.put("groupId", checklistAnswer.getGroup().getId());
    variables.put("created_by", checklistAnswer.getCreatedBy().getId());

    if (checklistAnswer.getSid() != null && checklistAnswer.getDescription() != null) {
      variables.put("sid", checklistAnswer.getSid());
      variables.put("name", checklistAnswer.getDescription());
    }

    if (checklistAnswer.getAnswer() == Answer.YES) {
      variables.put("ready", true);
    }

    return variables;
  }
}
