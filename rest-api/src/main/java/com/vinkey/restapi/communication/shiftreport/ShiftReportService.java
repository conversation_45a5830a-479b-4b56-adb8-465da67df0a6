package com.vinkey.restapi.communication.shiftreport;

import com.openhtmltopdf.pdfboxout.PdfRendererBuilder;
import com.vinkey.restapi.common.persistence.RowNumberCTE;
import com.vinkey.restapi.common.persistence.filter.FilterDecoder;
import com.vinkey.restapi.common.persistence.filter.SpecFilterCriteria;
import com.vinkey.restapi.communication.shiftreport.log.dto.ShiftReportLogPdfRead;
import com.vinkey.restapi.communication.shiftreport.notification.event.ShiftReportCanceledEvent;
import com.vinkey.restapi.communication.shiftreport.notification.event.ShiftReportClosedEvent;
import com.vinkey.restapi.communication.shiftreport.notification.event.ShiftReportHandoverStartedEvent;
import com.vinkey.restapi.communication.shiftreport.notification.event.ShiftReportUpdatedEvent;
import com.vinkey.restapi.communication.shiftreport.subtopic.ShiftReportSubTopicService;
import com.vinkey.restapi.communication.topic.TopicMapper;
import com.vinkey.restapi.communication.topic.dto.TopicPdfRead;
import com.vinkey.restapi.communication.topic.subtopic.dto.SubTopicPdfRead;
import com.vinkey.restapi.flowable.process.ProcessInstanceCustom;
import com.vinkey.restapi.flowable.task.TaskService;
import com.vinkey.restapi.flowable.task.history.TaskHistory;
import com.vinkey.restapi.identityandaccess.group.Group;
import com.vinkey.restapi.identityandaccess.group.GroupRepository;
import com.vinkey.restapi.permittowork.workpermit.PDFException;
import java.io.IOException;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Queue;
import java.util.stream.Collectors;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.engine.runtime.ProcessInstanceBuilder;
import org.jsoup.Jsoup;
import org.jsoup.helper.W3CDom;
import org.jsoup.nodes.Document;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.security.access.prepost.PostAuthorize;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thymeleaf.ITemplateEngine;
import org.thymeleaf.context.Context;

@Service
public class ShiftReportService {
  private final ApplicationEventPublisher applicationEventPublisher;
  private final ShiftReportRepository shiftReportRepository;
  private final ShiftReportSubTopicService shiftReportSubTopicService;
  private final RuntimeService runtimeService;
  private final GroupRepository groupRepository;
  private final ITemplateEngine templateEngine;
  private final TaskService taskServiceCustom;
  private final TopicMapper topicMapper = TopicMapper.INSTANCE;

  @Value("${pagination.defaultpageSize}")
  private Long pageSize = 20L;

  @Value("${pagination.defaultpageNumber}")
  private Long pageNumber = 0L;

  public ShiftReportService(
      ApplicationEventPublisher applicationEventPublisher,
      ShiftReportRepository shiftReportRepository,
      ShiftReportSubTopicService shiftReportSubTopicService,
      GroupRepository groupRepository,
      RuntimeService runtimeService,
      ITemplateEngine templateEngine,
      TaskService taskServiceCustom) {
    this.applicationEventPublisher = applicationEventPublisher;
    this.shiftReportRepository = shiftReportRepository;
    this.shiftReportSubTopicService = shiftReportSubTopicService;
    this.groupRepository = groupRepository;
    this.runtimeService = runtimeService;
    this.templateEngine = templateEngine;
    this.taskServiceCustom = taskServiceCustom;
  }

  @PostAuthorize(
      "hasRole('TENANT_ADMIN') or @membershipService.hasPrivilege(returnObject.group.id, authentication.principal.userId, 'SHIFT_REPORT_READ')")
  public ShiftReport getShiftReport(Long id, Long tenantId) {
    Optional<ShiftReport> shiftReport = shiftReportRepository.findByIdAndTenantId(id, tenantId);

    if (shiftReport.isEmpty()) {
      throw new ShiftReportNotFoundException(id);
    }
    return shiftReport.get();
  }

  public Long retrieveFilterKeyValue(List<SpecFilterCriteria> decodedFilter, String key) {
    if (decodedFilter == null) {
      return null;
    }

    List<Long> allEntries =
        decodedFilter.stream()
            .filter(f -> f.getKey().equals(key))
            .map(f -> Long.valueOf((String) f.getValue()))
            .distinct()
            .collect(Collectors.toList());
    if (allEntries.isEmpty()) {
      return null;
    }
    return allEntries.get(0);
  }

  @PreAuthorize(
      """
           hasRole('TENANT_ADMIN')
           OR @membershipService.hasPrivilege(#groupId, authentication.principal.userId, 'SHIFT_REPORT_READ')
           OR @membershipService.hasPrivilege(@shiftReportService.retrieveFilterKeyValue(T(com.vinkey.restapi.common.persistence.filter.FilterDecoder).decode(#filter), "groupId"), authentication.principal.userId, 'SHIFT_REPORT_READ')
           OR @membershipService.hasPrivilege(#ancestorGroupId, authentication.principal.userId, 'SHIFT_REPORT_READ')
           OR @membershipService.hasPrivilege(@shiftReportService.retrieveFilterKeyValue(T(com.vinkey.restapi.common.persistence.filter.FilterDecoder).decode(#filter), "ancestorGroupId"), authentication.principal.userId, 'SHIFT_REPORT_READ')
        """)
  public Page<ShiftReport> getShiftReportPage(
      Long tenantId,
      Long groupId,
      ShiftReportStatus status,
      Long startDateGte,
      Long startDateLte,
      Long endDateGte,
      Long endDateLte,
      Long ancestorGroupId,
      Long createdBy,
      List<String> candidateGroups,
      String search,
      String filter,
      String sort,
      Long pageSize,
      Long pageNumber) {
    pageSize = pageSize == null ? this.pageSize : pageSize;
    pageNumber = pageNumber == null ? this.pageNumber : pageNumber;

    Sort sortBy = Sort.by(Sort.Direction.DESC, ShiftReport_.START_TIME, ShiftReport_.ID);
    if (sort != null && !sort.isEmpty()) {
      List<Sort.Order> sortOrders =
          com.vinkey.restapi.common.persistence.sort.SortDecoder.decode(
                  sort, ShiftReportSortBy.class)
              .stream()
              .toList();
      if (!sortOrders.isEmpty()) {
        sortBy = Sort.by(sortOrders);
      }
    }

    Pageable pageable = PageRequest.of(pageNumber.intValue(), pageSize.intValue(), sortBy);

    ShiftReportSpecificationBuilder builder =
        new ShiftReportSpecificationBuilder()
            .tenant(tenantId)
            .group(groupId)
            .ancestorGroup(ancestorGroupId)
            .status(status)
            .startDateGte(startDateGte)
            .startDateLte(startDateLte)
            .endDateGte(endDateGte)
            .endDateLte(endDateLte)
            .createdBy(createdBy)
            .candidateGroups(candidateGroups)
            .search(search);

    if (filter != null) {
      Queue<SpecFilterCriteria> filterSpecs = FilterDecoder.decode(filter);
      while (!filterSpecs.isEmpty()) {
        builder.withFilter(filterSpecs.poll());
      }
    }

    return shiftReportRepository.findAll(builder.build(), pageable);
  }

  @PreAuthorize(
      "hasRole('TENANT_ADMIN') OR @membershipService.hasPrivilege(#shiftReport.getGroup().getId(), authentication.principal.userId, 'SHIFT_REPORT_CREATE')")
  @Transactional
  public Long createShiftReport(ShiftReport shiftReport) {
    Long tenantId = shiftReport.getTenant().getId();
    ProcessInstanceBuilder processInstanceBuilder =
        runtimeService
            .createProcessInstanceBuilder()
            .processDefinitionKey("shiftReportProcess")
            .tenantId(tenantId.toString());

    ProcessInstance processInstance = processInstanceBuilder.start();
    String processInstanceId = processInstance.getProcessInstanceId();

    ProcessInstanceCustom processInstanceCustom = new ProcessInstanceCustom();
    processInstanceCustom.setId(processInstance.getId());
    processInstanceCustom.setProcessInstanceId(processInstanceId);
    shiftReport.setProcessInstance(processInstanceCustom);

    Group group =
        groupRepository.findByIdAndTenantId(shiftReport.getGroup().getId(), tenantId).orElseThrow();
    shiftReport.setGroup(group);

    shiftReportRepository.save(shiftReport);
    this.shiftReportSubTopicService.createAllSubtopicShiftReport(shiftReport);

    Map<String, Object> variables = new HashMap<String, Object>();
    variables.put("groupId", shiftReport.getGroup().getId());
    variables.put("id", shiftReport.getId());
    variables.put("sid", shiftReport.getSid());
    variables.put("name", shiftReport.getGroup().getName());
    variables.put("created_by", shiftReport.getCreatedBy().getId());
    variables.put("ready", true);

    runtimeService.setVariables(processInstanceId, variables);
    return shiftReport.getId();
  }

  @PreAuthorize(
      "hasRole('TENANT_ADMIN') or @membershipService.hasPrivilege(#shiftReport.getGroup().getId(), authentication.principal.userId, 'SHIFT_REPORT_UPDATE')")
  @Transactional
  public ShiftReport updateShiftReport(ShiftReport shiftReport) {
    if (Boolean.TRUE.equals(shiftReport.getLocked())) {
      throw new ShiftReportLockedException();
    }

    // ? Quick fix (dirty) for audit tables to work.
    shiftReport.setModifiedDate(-1L);
    ShiftReport savedShiftReport = shiftReportRepository.save(shiftReport);
    applicationEventPublisher.publishEvent(new ShiftReportUpdatedEvent(savedShiftReport.getId()));
    return getShiftReport(savedShiftReport.getId(), savedShiftReport.getTenant().getId());
  }

  @PreAuthorize(
      "hasRole('TENANT_ADMIN') or @membershipService.hasPrivilege(#shiftReport.getGroup().getId(), authentication.principal.userId, 'SHIFT_REPORT_DELETE')")
  public Boolean checkDeletable(ShiftReport shiftReport) {
    return !shiftReportRepository.existsAnyRelationsById(shiftReport.getId());
  }

  @PreAuthorize(
      "hasRole('TENANT_ADMIN') or @membershipService.hasPrivilege(#shiftReport.getGroup().getId(), authentication.principal.userId, 'SHIFT_REPORT_DELETE')")
  public void deleteShiftReport(ShiftReport shiftReport) {
    if (!checkDeletable(shiftReport)) {
      throw new ShiftReportDeletionException(shiftReport.getId());
    }
    shiftReportRepository.delete(shiftReport);
  }

  @PreAuthorize(
      "hasRole('TENANT_ADMIN') or @membershipService.hasPrivilege(#shiftReport.getGroup().getId(), authentication.principal.userId, 'SHIFT_REPORT_CANCEL')  or #shiftReport.getCreatedBy().getId().equals(authentication.principal.userId)")
  @Transactional
  public void cancelShiftReport(ShiftReport shiftReport) {
    shiftReport.setLocked(true);
    shiftReport.setStatus(ShiftReportStatus.CANCELED);
    shiftReportRepository.save(shiftReport);
    if (shiftReport.getProcessInstance() != null) {
      runtimeService.deleteProcessInstance(
          shiftReport.getProcessInstance().getId(), "Shift report canceled by user");
    }
    applicationEventPublisher.publishEvent(new ShiftReportCanceledEvent(shiftReport.getId()));
  }

  public void lockShiftReport(Long id, Long tenantId) {
    ShiftReport shiftReport = getShiftReport(id, tenantId);
    shiftReport.setLocked(true);
    shiftReportRepository.save(shiftReport);
  }

  @Transactional
  public void closeShifReport(Long id, Long tenantId) {
    ShiftReport shiftReport = getShiftReport(id, tenantId);
    shiftReport.setStatus(ShiftReportStatus.CLOSED);
    shiftReportRepository.save(shiftReport);
    applicationEventPublisher.publishEvent(new ShiftReportClosedEvent(id));
  }

  @Transactional
  public void startHandoverShiftReport(Long id, Long tenantId) {
    ShiftReport shiftReport = getShiftReport(id, tenantId);
    shiftReport.setStatus(ShiftReportStatus.HANDOVER_STARTED);
    shiftReportRepository.save(shiftReport);
    applicationEventPublisher.publishEvent(
        new ShiftReportHandoverStartedEvent(shiftReport.getId()));
  }

  public List<RowNumberCTE> getShiftReportNeighbours(
      Long id,
      Long tenantId,
      Long groupId,
      ShiftReportStatus status,
      Long startDateGte,
      Long startDateLte,
      Long endDateGte,
      Long endDateLte,
      Long ancestorGroupId,
      Long createdBy,
      List<String> candidateGroups,
      String search,
      String filter) {
    return shiftReportRepository.findNeighbours(
        id,
        tenantId,
        groupId,
        status,
        startDateGte,
        startDateLte,
        endDateGte,
        endDateLte,
        ancestorGroupId,
        createdBy,
        candidateGroups,
        search,
        filter,
        Sort.by(Sort.Order.asc(ShiftReport_.START_TIME), Sort.Order.asc(ShiftReport_.ID)));
  }

  private Context generateShiftReportPdfContext(ShiftReport shiftReport, String timeZone) {
    List<TaskHistory> historyTasks =
        taskServiceCustom.getHistory(
            shiftReport.getProcessInstance().getId(), shiftReport.getTenant().getId());
    Map<String, List<TaskHistory>> multiValueMap = new HashMap<>();
    historyTasks.stream()
        .forEach(
            task -> {
              if (multiValueMap.containsKey(task.getTaskDefinitionKey()))
                multiValueMap.get(task.getTaskDefinitionKey()).add(task);
              else
                multiValueMap.put(
                    task.getTaskDefinitionKey(), new ArrayList<>(Arrays.asList(task)));
            });

    List<TaskHistory> taskOnly =
        multiValueMap.values().stream()
            .map(taskList -> taskList.stream().filter(task -> task.getEndTime() != null).toList())
            .filter(taskList -> !taskList.isEmpty())
            .map(
                tasks ->
                    Collections.max(
                        tasks, Comparator.comparing(task -> task.getEndTime().getTime())))
            .collect(Collectors.toList());

    if (!taskOnly.isEmpty())
      Collections.sort(taskOnly, Comparator.comparing(TaskHistory::getEndTime));

    List<TopicPdfRead> topics =
        topicMapper.shiftReportSubTopicToTopic(shiftReport.getShiftReportSubTopic());

    topics.stream()
        .forEach(
            topic -> {
              topic.setSubTopics(
                  topic.getSubTopics().stream()
                      .filter(subTopic -> !subTopic.getShiftReportLogs().isEmpty())
                      .collect(Collectors.toList()));
            });
    topics.removeIf(topic -> topic.getSubTopics().isEmpty());

    // ? Sort topics, sub topics and logs
    topics =
        topics.stream()
            .sorted(Comparator.comparing(TopicPdfRead::getOrder))
            .map(
                topic -> {
                  topic.setSubTopics(
                      topic.getSubTopics().stream()
                          .sorted(Comparator.comparing(SubTopicPdfRead::getOrder))
                          .map(
                              subTopic -> {
                                subTopic.setShiftReportLogs(
                                    subTopic.getShiftReportLogs().stream()
                                        .sorted(
                                            Comparator.comparing(
                                                ShiftReportLogPdfRead::getCreationDate))
                                        .collect(Collectors.toList()));
                                return subTopic;
                              })
                          .collect(Collectors.toList()));
                  return topic;
                })
            .collect(Collectors.toList());

    Context context = new Context();
    context.setVariable("status", shiftReport.getStatus().getValue());
    context.setVariable("shiftReport", shiftReport);
    context.setVariable("tasks", taskOnly);
    context.setVariable("timeZone", timeZone);
    context.setVariable("topics", topics);
    return context;
  }

  @PreAuthorize(
      "hasRole('TENANT_ADMIN') or @membershipService.hasPrivilege(#shiftReport.getGroup().getId(), authentication.principal.userId, 'SHIFT_REPORT_READ')")
  @Transactional(readOnly = true)
  public void generateShiftReportPdf(ShiftReport shiftReport, String timeZone, OutputStream out) {
    Context context = this.generateShiftReportPdfContext(shiftReport, timeZone);
    String html = templateEngine.process("pdf/shift-report.html", context);
    Document document = Jsoup.parse(html, "UTF-8");
    document.outputSettings().syntax(Document.OutputSettings.Syntax.xml);

    // take the copy of the stream and re-write it to an InputStream
    // try-with-resources here
    // putting the try block outside the Thread will cause the
    // PipedOutputStream resource to close before the Runnable finishes
    try (out) {
      PdfRendererBuilder builder = new PdfRendererBuilder();

      builder.withUri("shift-report.pdf");
      builder.toStream(out);
      builder.withW3cDocument(new W3CDom().fromJsoup(document), "/");
      builder.run();
    } catch (IOException e) {
      // logging and exception handling should go here
      e.printStackTrace();
      throw new PDFException();
    }
  }
}
