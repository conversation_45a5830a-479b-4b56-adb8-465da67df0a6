package com.vinkey.restapi.communication.shiftreport;

import com.vinkey.restapi.common.persistence.sort.SortByEnum;
import com.vinkey.restapi.identityandaccess.group.Group_;
import com.vinkey.restapi.identityandaccess.user.User_;

public enum ShiftReportSortBy implements SortByEnum {
  ID(ShiftReport_.ID),
  SID(ShiftReport_.SID),
  START_DATE(ShiftReport_.START_TIME),
  STATUS(ShiftReport_.STATUS),
  GROUP(ShiftReport_.GROUP + "." + Group_.NAME),
  CREATED_BY(ShiftReport_.CREATED_BY + "." + User_.FULL_NAME);

  private final String field;

  ShiftReportSortBy(String field) {
    this.field = field;
  }

  @Override
  public String getField() {
    return field;
  }
}
