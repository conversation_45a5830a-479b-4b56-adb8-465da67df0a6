package com.vinkey.restapi.communication.shiftreport;

import com.vinkey.restapi.common.persistence.Deletable;
import com.vinkey.restapi.common.persistence.NeighbourResult;
import com.vinkey.restapi.common.persistence.PaginatedResult;
import com.vinkey.restapi.communication.shiftreport.dto.ShiftReportChange;
import com.vinkey.restapi.communication.shiftreport.dto.ShiftReportCreate;
import com.vinkey.restapi.communication.shiftreport.dto.ShiftReportListRead;
import com.vinkey.restapi.communication.shiftreport.dto.ShiftReportRead;
import com.vinkey.restapi.communication.shiftreport.dto.ShiftReportUpdate;
import com.vinkey.restapi.identityandaccess.auth.dto.JwtDetails;
import java.io.IOException;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/v1/shift-reports")
public class ShiftReportController {
  private final ShiftReportService shiftReportService;
  private final ShiftReportHistoryService shiftReportHistoryService;
  private final ShiftReportMapper shiftReportMapper;

  public ShiftReportController(
      ShiftReportService shiftReportService, ShiftReportHistoryService shiftReportHistoryService) {
    this.shiftReportService = shiftReportService;
    this.shiftReportHistoryService = shiftReportHistoryService;
    this.shiftReportMapper = ShiftReportMapper.INSTANCE;
  }

  @PostMapping
  @ResponseStatus(HttpStatus.CREATED)
  public ShiftReportRead createShiftReport(
      @Valid @RequestBody ShiftReportCreate shiftReportCreate,
      @AuthenticationPrincipal JwtDetails jwtDetails) {

    Long tenantId = jwtDetails.getTenantId();

    ShiftReport shiftReport =
        shiftReportMapper.shiftReportCreateToShiftReport(shiftReportCreate, tenantId);

    Long shiftReportId = shiftReportService.createShiftReport(shiftReport);

    return shiftReportMapper.shiftReportToShiftReportRead(
        shiftReportService.getShiftReport(shiftReportId, tenantId));
  }

  @GetMapping("/{id}")
  @ResponseStatus(HttpStatus.OK)
  public ShiftReportRead getShiftReport(
      @PathVariable Long id, @AuthenticationPrincipal JwtDetails jwtDetails) {
    ShiftReport shiftReport = shiftReportService.getShiftReport(id, jwtDetails.getTenantId());
    return shiftReportMapper.shiftReportToShiftReportRead(shiftReport);
  }

  @GetMapping
  @ResponseStatus(HttpStatus.OK)
  public PaginatedResult<ShiftReportListRead> getShifReportPage(
      @RequestParam(required = false) Long groupId,
      @RequestParam(required = false) Long ancestorGroupId,
      @RequestParam(required = false) Long startDateLte,
      @RequestParam(required = false) Long startDateGte,
      @RequestParam(required = false) Long endDateGte,
      @RequestParam(required = false) Long endDateLte,
      @RequestParam(required = false) ShiftReportStatus status,
      @RequestParam(required = false) Long createdBy,
      @RequestParam(required = false) List<String> candidateGroups,
      @RequestParam(required = false) String search,
      @RequestParam(required = false) String filter,
      @RequestParam(required = false) String sort,
      @RequestParam(required = false) Long pageNumber,
      @RequestParam(required = false) Long pageSize,
      @AuthenticationPrincipal JwtDetails jwtDetails) {
    Page<ShiftReport> shiftReportPage =
        shiftReportService.getShiftReportPage(
            jwtDetails.getTenantId(),
            groupId,
            status,
            startDateGte,
            startDateLte,
            endDateGte,
            endDateLte,
            ancestorGroupId,
            createdBy,
            candidateGroups,
            search,
            filter,
            sort,
            pageSize,
            pageNumber);

    return shiftReportMapper.paginatedShiftReportsToPaginatedShiftReportListReads(shiftReportPage);
  }

  @GetMapping("/{id}/deletable")
  @ResponseStatus(HttpStatus.OK)
  public Deletable getShiftReportDeletable(
      @PathVariable Long id, @AuthenticationPrincipal JwtDetails jwtDetails) {
    ShiftReport shiftReport = shiftReportService.getShiftReport(id, jwtDetails.getTenantId());
    Deletable deletable = new Deletable();
    deletable.setDeletable(shiftReportService.checkDeletable(shiftReport));
    return deletable;
  }

  @PutMapping("/{id}")
  @ResponseStatus(HttpStatus.OK)
  public ShiftReportRead updateShiftReport(
      @PathVariable Long id,
      @Valid @RequestBody ShiftReportUpdate shiftReportUpdate,
      @AuthenticationPrincipal JwtDetails jwtDetails) {

    ShiftReport shiftReport = shiftReportService.getShiftReport(id, jwtDetails.getTenantId());
    shiftReportMapper.updateShiftReportFromShiftReportUpdate(shiftReport, shiftReportUpdate);
    ShiftReport updatedShiftReport = shiftReportService.updateShiftReport(shiftReport);
    return shiftReportMapper.shiftReportToShiftReportRead(updatedShiftReport);
  }

  @DeleteMapping("/{id}")
  @ResponseStatus(HttpStatus.NO_CONTENT)
  public void deleteShiftReport(
      @PathVariable Long id, @AuthenticationPrincipal JwtDetails jwtDetails) {
    ShiftReport shiftReport = shiftReportService.getShiftReport(id, jwtDetails.getTenantId());
    shiftReportService.deleteShiftReport(shiftReport);
  }

  @GetMapping(value = "/{id}/history")
  @ResponseBody
  public List<ShiftReportChange> getHistory(
      @PathVariable Long id, @AuthenticationPrincipal JwtDetails jwtDetails) {
    return shiftReportHistoryService.getHistory(id, jwtDetails.getTenantId());
  }

  @PostMapping("/{id}/cancel")
  @ResponseStatus(HttpStatus.NO_CONTENT)
  public void cancelShiftReport(
      @PathVariable Long id, @AuthenticationPrincipal JwtDetails jwtDetails) {
    ShiftReport shiftReport = shiftReportService.getShiftReport(id, jwtDetails.getTenantId());
    shiftReportService.cancelShiftReport(shiftReport);
  }

  @GetMapping("/{id}/neighbours")
  @ResponseStatus(HttpStatus.OK)
  public NeighbourResult getShiftReportNeighbours(
      @PathVariable Long id,
      @RequestParam(required = false) Long groupId,
      @RequestParam(required = false) ShiftReportStatus status,
      @RequestParam(required = false) Long startDateGte,
      @RequestParam(required = false) Long startDateLte,
      @RequestParam(required = false) Long endDateGte,
      @RequestParam(required = false) Long endDateLte,
      @RequestParam(required = false) Long ancestorGroupId,
      @RequestParam(required = false) Long createdBy,
      @RequestParam(required = false) List<String> candidateGroups,
      @RequestParam(required = false) String search,
      @RequestParam(required = false) String filter,
      @AuthenticationPrincipal JwtDetails jwtDetails) {
    return shiftReportMapper.listOfRowNumberCTEToNeighbourResult(
        shiftReportService.getShiftReportNeighbours(
            id,
            jwtDetails.getTenantId(),
            groupId,
            status,
            startDateGte,
            startDateLte,
            endDateGte,
            endDateLte,
            ancestorGroupId,
            createdBy,
            candidateGroups,
            search,
            filter),
        id);
  }

  @GetMapping(value = "/{id}/pdf", produces = MediaType.APPLICATION_PDF_VALUE)
  @ResponseBody
  public void generateShiftReportPdf(
      @PathVariable Long id,
      @RequestParam(required = false) String timeZone,
      @AuthenticationPrincipal JwtDetails jwtDetails,
      HttpServletResponse response)
      throws IOException {
    ShiftReport shiftReport = shiftReportService.getShiftReport(id, jwtDetails.getTenantId());
    response.setContentType(MediaType.APPLICATION_PDF_VALUE);
    response.addHeader(
        HttpHeaders.CONTENT_DISPOSITION,
        "attachment; filename=shiftReport " + shiftReport.getSid() + ".pdf");
    shiftReportService.generateShiftReportPdf(shiftReport, timeZone, response.getOutputStream());
  }
}
